<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>.:Hotspot YAPIP - Status:.</title>
  $(if refresh-timeout)
<meta http-equiv="refresh" content="$(refresh-timeout-secs)">
$(endif)

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta http-equiv="pragma" content="no-cache">
  <meta http-equiv="expires" content="-1" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="css/bootstrap.min.css">
  <script src="jquery/jquery.min.js"></script>
  <script src="js/bootstrap.min.js"></script>
  <script src="js/jquery.js"></script>
  <!-- Custom CSS -->
    <link href="css/font-awesome.min.css" rel="stylesheet" type="text/css">
    <link href="css/custom.css" rel="stylesheet">
    <link href="css/sb-admin-2.css" rel="stylesheet">
<!-- Background responsive -->
<style type="text/css">
    body {
    background-image: url(img/small-bg.jpg);
    background-size: cover;
}
@media only screen and (min-device-width: 500px){
    body{
        background-image: url(img/bg.jpg);
        background-repeat: no-repeat;
        background-size: cover;
    }
}
    </style>
	
	<script language="JavaScript">
<!--
$(if advert-pending == 'yes')
    var popup = '';
    function focusAdvert() {
	if (window.focus) popup.focus();
    }
    function openAdvert() {
	popup = open('$(link-advert)', 'hotspot_advert', '');
	setTimeout("focusAdvert()", 1000);
    }
$(endif)
    function openLogout() {
	if (window.name != 'hotspot_status') return true;
        open('$(link-logout)', 'hotspot_logout', 'toolbar=0,location=0,directories=0,status=0,menubars=0,resizable=1,width=280,height=250');
	window.close();
	return false;
    }
//-->
</script>
</head>

<body bottommargin="0" topmargin="0" leftmargin="0" rightmargin="0"
$(if advert-pending == 'yes')
	onLoad="openAdvert()"
$(endif)>

<div class="navbar-default navbar navbar-fixed-top" role="navigation">
    <div class="container">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand"style="color: #fff href="login.html">YAPIP</a>
        </div>
        
        <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
            <ul class="nav navbar-nav">
                <li><a href="login.html">Login <span class="sr-only">(current)</span></a></li>
                <li  class="active" ><a href="status.html">Status</a></li>
                
                
                
                
                <li><a href="logout.html?erase-cookie=true">Logout</a></li>
            </ul>   
        </div>
    </div>
</div>

</br></br></br>

<div class="container"> 
<div id="wrapper">
    <div id="page-content-wrapper">
      <div class="container-fluid">                         
        <div class="row" style="margin-bottom: 15px; margin-top: 10px;">      

		<!--panel -->
            <div class="panel-body" style="background-color: white; border: 1px solid #ddd; border-radius: 4px;">
            <center>
                <div class="alert alert-info" style="padding: 1px;"><h3>Anda berhasil Login</h3></div>
            </center>
			
    <div class="col-md-6 col-sm-12">
        <div class="row">
         $(if error)
                    <div class="alert alert-danger">$(error)</div>
                $(endif)

                $(if login-by == 'trial')
                    <div class="alert alert-info">selamat datang Tamu! </div>
                $(elif login-by != 'mac')
                    <div class="alert alert-info">Selamat datang $(username) anda sudah login !</div>
                $(endif)
        

        </div>

        <div class="row">            
            <div class="panel panel-default">
                <div class="panel-body">
                   <table class="table table-striped">
                            <tbody>
                            <tr>
                                <td>IP address:</td>
                                <td>$(ip)</td>
                            </tr>
                            <tr>
                                <td>bytes up/down</td>
                                <td>$(bytes-in-nice) / $(bytes-out-nice)</td>
                            </tr>
                            $(if session-time-left)
                            <tr>
                                <td>connected / left:</td>
                                <td>$(uptime) / $(session-time-left)</td>
                            </tr>
                            $(else)
                            <tr>
                                <td>connected:</td>
                                <td>$(uptime)</td>
                            </tr>
                            $(endif)
                            $(if refresh-timeout)
                            <tr>
                                <td>status refresh</td>
                                <td>$(refresh-timeout)</td>
                                $(endif)
                            </tbody>
                        </table>
						</table>
$(if login-by-mac != 'yes')

$(endif)
                
				 <div class="col-md-12">
                            <form action="$(link-logout)" name="logout" onSubmit="return openLogout()">
                                <div class="col-md-6 col-sm-6 col-lg-6">
                                    <btn class="btn btn-default btn-block" onclick="location.reload();">Refresh</btn>
                                </div>
                                <div class="col-md-6 col-sm-6 col-lg-6">
                                <input type="submit" value="Log Off" class="btn btn-danger btn-block">
                                    </div>
                                </form>
								</div>
            </div>
        </div>
    </div>
	</div>


<div class="row">
    <div class="col-md-6 col-sm-12">
        <div class="panel panel-default" style="margin-bottom: 10px;">
            <div class="panel-body">
            <div id="myCarousel" class="carousel slide" data-ride="carousel">
    
                <ol class="carousel-indicators">
                    <li data-target="#myCarousel" data-slide-to="0" class="active"></li>
                    <li data-target="#myCarousel" data-slide-to="1"></li>
                    <li data-target="#myCarousel" data-slide-to="2"></li>
                </ol>

                <div class="carousel-inner">
                    <div class="item active">
                        <img src="img/1.jpg" alt="Ical RedHat" style="width:100%; border-radius: 4px;">
                            <div class="carousel-caption">
                                <h3></h3>
                                <p></p>
                            </div>
                        </div>

                    <div class="item">
                        <img src="img/2.jpg" alt="Ical RedHat" style="width:100%; border-radius: 4px;">
                            <div class="carousel-caption">
                                <h3></h3>
                                <p></p>
                            </div>
                        </div>
    
                    <div class="item">
                        <img src="img/3.jpg" alt="Ical RedHat" style="width:100%; border-radius: 4px;">
                            <div class="carousel-caption">
                                <h3></h3>
                                <p></p>
                            </div>
                        </div>
                </div>
            </div>

            <div class="card hovercard">
                <!--<div class="cardheader"></div> -->
                                   
                    <div class="info">
                    <div class="title">
                        <font color=#31708f>SMP/SMA/SMK YAPIP Makassar Sungguminasa</font>

                        <div class="desc">Jl. Andi Mallombasang No. 40 C</div>
                        <div class="desc">Sungguminasa, Gowa, 92111</div>
                        <!--<div class="desc"><blink>IP Address : $(ip)</div>-->
                        
                    </div>   
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Placed at the end of the document so the pages load faster -->
<script src="/js/jquery-1.12.2.min.js"></script>
<script>window.jQuery || document.write('<script src="/js/jquery-1.12.2.min.js"><\/script>')</script>
<script src="/js/bootstrap.min.js"></script>


    </body>
</html>
