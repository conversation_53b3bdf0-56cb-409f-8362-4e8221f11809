<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
  <title>.:Hotspot YAPIP - Login:.</title>
  <meta http-equiv="expires" content="-1" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" href="css/bootstrap.min.css">
  <script src="jquery/jquery.min.js"></script>
  <script src="js/bootstrap.min.js"></script>
  <script src="js/jquery.js"></script>
  <!-- Custom CSS -->
    <link href="css/font-awesome.min.css" rel="stylesheet" type="text/css">
    <link href="css/custom.css" rel="stylesheet">
    <link href="css/sb-admin-2.css" rel="stylesheet">
<!-- Background responsive -->
<style type="text/css">
    body {
    background-image: url(img/small-bg.jpg);
    background-size: cover;
}
@media only screen and (min-device-width: 500px){
    body{
        background-image: url(img/bg.jpg);
        background-repeat: no-repeat;
        background-size: cover;
    }
}
    </style>
</head>
<body>
$(if chap-id)
	<form name="sendin" action="$(link-login-only)" method="post">
		<input type="hidden" name="username" />
		<input type="hidden" name="password" />
		<input type="hidden" name="dst" value="$(link-orig)" />
		<input type="hidden" name="popup" value="true" />
	</form>
	
	<script type="text/javascript" src="/md5.js"></script>
	<script type="text/javascript">
	<!--
	    function doLogin() {
		document.sendin.username.value = document.login.username.value;
		document.sendin.password.value = hexMD5('$(chap-id)' + document.login.password.value + '$(chap-challenge)');
		document.sendin.submit();
		return false;
	    }
	//-->
	</script>
$(endif)

<div class="navbar-default navbar navbar-fixed-top" role="navigation">
    <div class="container">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand"style="color: #fff href="login.html">YAPIP</a>
        </div>
        
        <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
            <ul class="nav navbar-nav">
                <li class="active"><a href="login.html">Login <span class="sr-only">(current)</span></a></li>
                <li><a href="status.html">Status</a></li>
                
                
                
                
                <li><a href="logout.html?erase-cookie=true">Logout</a></li>
            </ul>   
        </div>
    </div>
</div>

</br></br></br>

<div class="container"> 
<div id="wrapper">
    <div id="page-content-wrapper">
      <div class="container-fluid">                         
        <div class="row" style="margin-bottom: 15px; margin-top: 10px;">       <!--panel -->
            <div class="panel-body" style="background-color: white; border: 1px solid #ddd; border-radius: 4px;">
            <center>
                <div class="alert alert-info" style="padding: 1px;"><h3>SMP/SMA/SMK YAPIP Makassar Sungguminasa</h3></div>
            </center>
    
    <div class="col-md-6 col-sm-12">
        <div class="row">
            $(if error)
            <div class="alert alert-danger">Terjadi kesalahan: $(error)</div>
            $(endif)

          <div class="alert alert-success">Selamat Datang di Layanan Hotspot YAPIP. </br> Silahkan login dulu untuk menikmati akses layanan internet berbasis hotspot.</div>
 <div class="alert alert-danger"> Jika tidak mempunyai akun, silahkan klik tombol Tamu untuk mendapatkan akses internet gratis</div>
        </div>

        <div class="row">            
            <div class="panel panel-default">
                <div class="panel-body">
                    <form id="loginForm" class="form-horizontal" role="form" action="$(link-login-only)" method="post">
                        <input type="hidden" name="dst" value="$(link-orig)"/>
                        <input type="hidden" name="popup" value="true"/>
                        <div class="form-group">
                            <label for="inputLogin" class="col-sm-2 control-label">Username</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control input-lg" id="inputLogin" name="username" value="$(username)"
                                           placeholder="Username" autofocus required>
                                </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="inputPassword" class="col-sm-2 control-label">Password</label>
                                <div class="col-sm-10">
                                  <input type="password" class="form-control input-lg" id="inputPassword" name="password" placeholder="Password" required>
                                </div>
                        </div>                           
                        
 



                        </br>

                        <div class="form-group">
                            <div class="col-sm-offset-0 col-sm-12">
                                <button type="submit" class="btn btn-success btn-block btn-lg">LOGIN</button>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-sm-offset-0 col-sm-12">
                                <button type="button" class="btn btn-info btn-block btn-lg" data-toggle="modal" data-target="#guestModal">LOGIN TAMU</button>
                            </div>
                        </div>
						$(if trial == 'yes')  <!-- Trigger the modal with a button -->
  <button type="button" class="btn btn-success btn-block btn-lg" data-toggle="modal" data-target="#myModal">TRIAL</button>

  <!-- Modal -->
  <div class="modal fade" id="myModal" role="dialog">
    <div class="modal-dialog">
    
      <!-- Modal content-->
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal">&times;</button>
          <h4 class="modal-title">FREE INTERNET</h4>
        </div>
        <div class="modal-body">
          <p>
            <div class="desc">Syarat & Ketentuan:</div>
            <div class="desc">1. Akses internet terbatas.</div>
            <div class="desc">2. Cuma bisa digunakan untuk mengakses konten-konten tertentu seperti Whatsapp, google, dan beberapa konten pelajaran lainnya.</div>
            <div class="desc">3. Akses ke sosial media, youtube dan sejenisnya akan diblokir pada jam pelajaran, tapi akan terbuka pada jam istirahat.</div>
            4. Jika paham dengan syarat & ketentuan yang telah diberikan, silahkan <a style="color: #FF8080"href="$(link-login-only)?dst=$(link-orig-esc)&amp;username=T-$(mac-esc)">KLIK DISINI</a>.
		untuk terhubung ke jaringan internet.</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        </div>
      </div>
      
    </div>
  </div>
$(endif)

  <!-- Guest Login Modal -->
  <div class="modal fade" id="guestModal" role="dialog">
    <div class="modal-dialog modal-lg">
      <!-- Modal content-->
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal">&times;</button>
          <h4 class="modal-title">Syarat & Ketentuan Penggunaan Internet Tamu</h4>
        </div>
        <div class="modal-body">
          <div class="alert alert-info">
            <strong>Perhatian:</strong> Silahkan baca dan setujui syarat & ketentuan berikut sebelum menggunakan akses internet tamu.
          </div>

          <div id="termsContent" style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 15px; background-color: #f9f9f9;">
            <div class="text-center">
              <i class="fa fa-spinner fa-spin"></i> Memuat syarat & ketentuan...
            </div>
          </div>

          <div style="margin-top: 15px;">
            <div class="checkbox">
              <label>
                <input type="checkbox" id="agreeTerms">
                <strong>Saya telah membaca dan menyetujui semua syarat & ketentuan penggunaan internet di atas</strong>
              </label>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">Batal</button>
          <button type="button" class="btn btn-success" id="proceedGuestLogin" disabled>Lanjutkan Login Tamu</button>
        </div>
      </div>
    </div>
  </div>

                        <center>
                            <div  class="alert alert-success">Gunakan Internet Dengan Bijak !
                                <div style="color: #a1c1c1; font-size: 10px">Powered by MikroTik RouterOS</div>
                            </div>
                        </center>
                    </form>
                </div>
            </div>
        </div>
    </div>

<div class="row">
    <div class="col-md-6 col-sm-12">
        <div class="panel panel-default" style="margin-bottom: 10px;">
            <div class="panel-body">
            <div id="myCarousel" class="carousel slide" data-ride="carousel">
    
                <ol class="carousel-indicators">
                    <li data-target="#myCarousel" data-slide-to="0" class="active"></li>
                    <li data-target="#myCarousel" data-slide-to="1"></li>
                    <li data-target="#myCarousel" data-slide-to="2"></li>
                </ol>

                <div class="carousel-inner">
                    <div class="item active">
                        <img src="img/1.jpg" alt="Ical RedHat" style="width:100%; border-radius: 4px;">
                            <div class="carousel-caption">
                                <h3></h3>
                                <p></p>
                            </div>
                        </div>

                    <div class="item">
                        <img src="img/2.jpg" alt="Ical RedHat" style="width:100%; border-radius: 4px;">
                            <div class="carousel-caption">
                                <h3></h3>
                                <p></p>
                            </div>
                        </div>
    
                    <div class="item">
                        <img src="img/3.jpg" alt="Ical RedHat" style="width:100%; border-radius: 4px;">
                            <div class="carousel-caption">
                                <h3></h3>
                                <p></p>
                            </div>
                        </div>
                </div>
            </div>

            <div class="card hovercard">
                <!--<div class="cardheader"></div> -->
                                   
                    <div class="info">
                    <div class="title">
                    <font color=#31708f>SMP/SMA/SMK YAPIP Makassar Sungguminasa</font>

                    <div class="desc">Jl. Andi Mallombasang No. 40 C</div>
                    <div class="desc">Sungguminasa, Gowa, 92111</div>
                                       
                        <!--<div class="desc"><blink>IP Address : $(ip)</div>-->
                        
                    </div>   
                </div>
            </div>
        </div>
    </div>
</div>



$(if chap-id)
    <script type="text/javascript" src="md5.js"></script>
    <script type="text/javascript">
        $('#loginForm').submit(function () {
            var password = $('#inputPassword');
            password.val(hexMD5('$(chap-id)' + password.val() + '$(chap-challenge)'));
        });
    </script>
$(endif)

<!--
</div></div></div></div></div></div></div>
<div id="footer">
    <div class="container">
        <p class="text-muted">Powered by MikroTik RouterOS</p>
    </div>
</div>
-->
<script>
    // Guest login functionality
    $(document).ready(function() {
        // Load terms and conditions from embedded data
        function loadTermsFromData() {
            try {
                // Data aturan penggunaan internet
                var rulesData = [
                    {title: "Batas Kecepatan", description: "Kecepatan internet Anda dibatasi maksimal 2 Mbps"},
                    {title: "Jam Belajar - Akses Diizinkan", description: "✅ DIIZINKAN: Akses untuk materi pelajaran Google WhatsApp dan Telegram"},
                    {title: "Jam Belajar - Akses Diblokir", description: "❌ DIBLOKIR: Semua Media Sosial (Facebook Instagram TikTok dll) dan semua Game Online"},
                    {title: "Jam Istirahat & Pulang Sekolah", description: "✅ AKSES PENUH: Semua blokir akan dinonaktifkan. Anda bisa mengakses internet dengan bebas"},
                    {title: "Jadwal Senin - Kamis (Blokir Aktif)", description: "BLOKIR AKTIF: 07:30 - 10:10 | 10:30 - 12:30 | 13:00 - 15:00"},
                    {title: "Jadwal Senin - Kamis (Akses Penuh)", description: "AKSES PENUH: 10:10 - 10:30 | 12:30 - 13:00 | setelah 15:00"},
                    {title: "Jadwal Jumat (Blokir Aktif)", description: "BLOKIR AKTIF: 07:30 - 09:30 | 10:00 - 11:20"},
                    {title: "Jadwal Jumat (Akses Penuh)", description: "AKSES PENUH: 09:30 - 10:00 | setelah 11:20"},
                    {title: "Jadwal Sabtu & Minggu", description: "✅ AKSES PENUH sepanjang hari"},
                    {title: "Persetujuan", description: "Dengan melanjutkan Anda setuju untuk mematuhi semua peraturan yang berlaku. Terima kasih"}
                ];

                var termsHtml = '<h5>Aturan Penggunaan Internet:</h5><ol>';

                for (var i = 0; i < rulesData.length; i++) {
                    termsHtml += '<li><strong>' + rulesData[i].title + '</strong><br>' + rulesData[i].description + '</li><br>';
                }

                termsHtml += '</ol>';
                $('#termsContent').html(termsHtml);

            } catch (error) {
                console.error('Error loading terms:', error);
                $('#termsContent').html('<div class="alert alert-warning">Gagal memuat syarat & ketentuan. Silahkan coba lagi.</div>');
            }
        }

        // Load terms when modal is shown
        $('#guestModal').on('show.bs.modal', function() {
            // Reset the content to loading state
            $('#termsContent').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Memuat syarat & ketentuan...</div>');

            // Load terms immediately (no delay needed since it's embedded data)
            setTimeout(function() {
                loadTermsFromData();
            }, 100);

            $('#agreeTerms').prop('checked', false);
            $('#proceedGuestLogin').prop('disabled', true);
        });

        // Enable/disable proceed button based on checkbox
        $('#agreeTerms').change(function() {
            $('#proceedGuestLogin').prop('disabled', !this.checked);
        });

        // Handle guest login
        $('#proceedGuestLogin').click(function() {
            if ($('#agreeTerms').is(':checked')) {
                $(if chap-id)
                    // CHAP authentication
                    if (typeof hexMD5 === 'function') {
                        document.sendin.username.value = 'siswa';
                        document.sendin.password.value = hexMD5('$(chap-id)' + 'siswa' + '$(chap-challenge)');
                        document.sendin.submit();
                    } else {
                        alert('Error: MD5 function not available');
                    }
                $(else)
                    // Regular authentication
                    var guestForm = $('<form>', {
                        'method': 'post',
                        'action': '$(link-login-only)'
                    });

                    // Add hidden fields
                    guestForm.append($('<input>', {
                        'type': 'hidden',
                        'name': 'username',
                        'value': 'siswa'
                    }));

                    guestForm.append($('<input>', {
                        'type': 'hidden',
                        'name': 'password',
                        'value': 'siswa'
                    }));

                    guestForm.append($('<input>', {
                        'type': 'hidden',
                        'name': 'dst',
                        'value': '$(link-orig)'
                    }));

                    guestForm.append($('<input>', {
                        'type': 'hidden',
                        'name': 'popup',
                        'value': 'true'
                    }));

                    // Append form to body and submit
                    $('body').append(guestForm);
                    guestForm.submit();
                $(endif)
            }
        });
    });

    $("#menu-toggle").click(function (e) {
        e.preventDefault();
    $("#wrapper").toggleClass("toggled");
    });
</script>
	<script type="text/javascript">
	<!--
		document.login.username.focus();
	//-->
	</script>
    </body>
</html>
