html, body {
    height: 100%;
}

/* Wrapper for page content to push down footer */
#wrap {
    min-height: 100%;
    height: auto;
    margin: 0 auto -60px;
    padding: 0 0 60px;
}

/* Set the fixed height of the footer here */
#footer {
    height: 60px;
    background-color: #f5f5f5;
}

#wrap > .container {
    padding: 15px 15px 0;
}
.container .text-muted {
    margin: 20px 0;
}

#footer > .container {
    padding-left: 15px;
    padding-right: 15px;
}

code {
    font-size: 80%;
}


/* new customizing */
.navbar {
    margin-bottom: 0px;
}

.navbar-inverse {
    background-color: #153E5D;
    border-color: #2B78C5;
}
.navbar-inverse .navbar-nav > .active > a, .navbar-inverse .navbar-nav > .active > a:hover, .navbar-inverse .navbar-nav > .active > a:focus {
    background-color: #0F8093;
}
#bottom-menu {
    background: url(../img/1.jpeg);
    padding: 0px 0px;
    height: 100px;
    color: #fff;
}
#bottom-menu .mylogo {
    width: 100px;
    float: left;
}
#bottom-menu .textlogo {
	padding: 15px 20px;
    width: 350px;
}

#footer {
    background-color: #551A8B;
}

.text-muted {
    color: #B7B7B7;
}
.textlogo h1 {
    font-family: "Arial",Helvetica,sans-serif;
    font-size: 2em;
    color: #fff;
    font-weight: bold;
    text-shadow: 3px 3px 0px rgba(0, 0, 0, 0.3);
}
.card {
    padding-top: 20px;
    margin: 0;
    background-color: rgba(214, 224, 226, 0.2);
    border-top-width: 0;
    border-bottom-width: 2px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.card .card-heading {
    padding: 0 20px;
    margin: 0;
}
.card .card-heading.simple {
    font-size: 20px;
    font-weight: 300;
    color: #777;
    border-bottom: 1px solid #e5e5e5;
}
.card .card-heading.image img {
    display: inline-block;
    width: 50px;
    height: 50px;
    margin-right: 15px;
    vertical-align: top;
    border: 0;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
}
.card .card-heading.image .card-heading-header {
    display: inline-block;
    vertical-align: top;
}
.card .card-heading.image .card-heading-header h3 {
    margin: 0;
    font-size: 14px;
    line-height: 16px;
    color: #262626;
}
.card .card-heading.image .card-heading-header span {
    font-size: 12px;
    color: #999999;
}
.card .card-body {
    padding: 0 20px;
    margin-top: 20px;
}
.card .card-media {
    padding: 0 20px;
    margin: 0 -14px;
}
.card .card-media img {
    max-width: 100%;
    max-height: 100%;
}
.card .card-actions {
    min-height: 30px;
    padding: 0 20px 20px 20px;
    margin: 20px 0 0 0;
}
.card .card-comments {
    padding: 20px;
    margin: 0;
    background-color: #f8f8f8;
}
.card .card-comments .comments-collapse-toggle {
    padding: 0;
    margin: 0 20px 12px 20px;
}
.card .card-comments .comments-collapse-toggle a,
.card .card-comments .comments-collapse-toggle span {
    padding-right: 5px;
    overflow: hidden;
    font-size: 12px;
    color: #999;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.card-comments .media-heading {
    font-size: 13px;
    font-weight: bold;
}
.card.people {
    position: relative;
    display: inline-block;
    width: 170px;
    height: 300px;
    padding-top: 0;
    margin-left: 20px;
    overflow: hidden;
    vertical-align: top;
}
.card.people:first-child {
    margin-left: 0;
}
.card.people .card-top {
    position: absolute;
    top: 0;
    left: 0;
    display: inline-block;
    width: 170px;
    height: 150px;
    background-color: #ffffff;
}
.card.people .card-top.green {
    background-color: #53a93f;
}
.card.people .card-top.blue {
    background-color: #427fed;
}
.card.people .card-info {
    position: absolute;
    top: 150px;
    display: inline-block;
    width: 100%;
    height: 100px;
    overflow: hidden;
    background: #ffffff;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.card.people .card-info .title {
    display: block;
    margin: 8px 14px 0 14px;
    overflow: hidden;
    font-size: 16px;
    font-weight: bold;
    line-height: 18px;
    color: #404040;
}
.card.people .card-info .desc {
    display: block;
    margin: 8px 14px 0 14px;
    overflow: hidden;
    font-size: 12px;
    line-height: 16px;
    color: #737373;
    text-overflow: ellipsis;
}
.card.people .card-bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    display: inline-block;
    width: 100%;
    padding: 10px 20px;
    line-height: 29px;
    text-align: center;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.card.hovercard {
    position: relative;
    padding-top: 5px;
    overflow: hidden;
    text-align: center;
    background-color: rgba(165, 35, 35, 0); /*#ffdf4e  */
}
.card.hovercard .cardheader {
    background: url("../img/img.jpg");
    background-size: 100%;
    background-repeat: no-repeat;
    width: auto;
    height: 280px;
}
.card.hovercard .avatar {
    position: relative;
    top: -1px;
    margin-bottom: -1px;
}
.card.hovercard .avatar img {
    width: 100px;
    height: 100px;
    max-width: 100px;
    max-height: 100px;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    border: 5px solid rgba(255,255,255,0.5);
}
.card.hovercard .info {
    padding: 4px 8px 0px;
}
.card.hovercard .info .title {
    margin-bottom: 4px;
    font-size: 20px;
    line-height: 1;
    color: #262626;
    vertical-align: middle;
}
.card.hovercard .info .desc {
    overflow: hidden;
    font-size: 12px;
    line-height: 20px;
    color: #080404;
    text-overflow: ellipsis;
}
.card.hovercard .bottom {
    padding: 0 20px;
    margin-bottom: 0px;
}
.card .btn{ border-radius: 10%; width:0px; height:0px; line-height:18px;  }